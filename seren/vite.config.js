import { defineConfig } from 'vite'
import { resolve } from 'path'
import {
  copyFileSync,
  mkdirSync,
  existsSync,
  cpSync,
  readFileSync,
  writeFileSync,
} from 'fs'

const copyStaticFiles = () => ({
  name: 'copy-static-files',
  writeBundle() {
    const packageJson = JSON.parse(
        readFileSync(resolve(__dirname, 'package.json'), 'utf8')
    )

    const manifestPath = resolve(__dirname, 'src/manifest.json')
    const manifest = JSON.parse(readFileSync(manifestPath, 'utf8'))

    manifest.version = packageJson.version

    const distManifestPath = resolve(__dirname, 'egg/manifest.json')
    const distDir = resolve(__dirname, 'egg')

    if (!existsSync(distDir)) {
      mkdirSync(distDir, { recursive: true })
    }

    writeFileSync(distManifestPath, JSON.stringify(manifest, null, 4))

    const staticFiles = [
      ['src/raven.html', 'egg/raven.html']
    ]

    staticFiles.forEach(([src, dest]) => {
      try {
        const destDir = resolve(__dirname, dest.substring(0, dest.lastIndexOf('/')))
        if (!existsSync(destDir)) {
          mkdirSync(destDir, { recursive: true })
        }
        copyFileSync(resolve(__dirname, src), resolve(__dirname, dest))
      } catch (error) {
        console.error(`Error copying ${src} to ${dest}:`, error)
      }
    })

    try {
      cpSync(
          resolve(__dirname, 'src/raven/styles'),
          resolve(__dirname, 'egg/raven/styles'),
          { recursive: true }
      )

      cpSync(resolve(__dirname, 'src/assets'), resolve(__dirname, 'egg/assets'), {
        recursive: true,
      })
    } catch (error) {
      console.error('Error copying directories:', error)
    }
  },
})

export default defineConfig({
  build: {
    outDir: 'egg',
    emptyOutDir: true,
    minify: false,
    sourcemap: true,
    rollupOptions: {
      input: {
        'page-clock': resolve(__dirname, 'src/sailor/page-clock.js'),
        raven: resolve(__dirname, 'src/raven.js'),
        reef: resolve(__dirname, 'src/reef.js'),
        seren: resolve(__dirname, 'src/seren.js'),
        actions: resolve(__dirname, 'src/sailor/actions.js'),
        'rc-rooms': resolve(__dirname, 'src/sailor/rc-rooms.js'),
        abilities: resolve(__dirname, 'src/sailor/abilities.js'),
        'requests-actions': resolve(__dirname, 'src/sailor/requests-actions.js'),
        'rc-page': resolve(__dirname, 'src/sailor/rc-page.js'),
        'storage': resolve(__dirname, 'src/sailor/storage.js'),
      },
      output: {
        entryFileNames: (chunkInfo) => {
          if (
              chunkInfo.name.startsWith('sailor/') ||
              [
                'actions',
                'rc-rooms',
                'navigator',
                'abilities',
                'page-clock',
                'rc-page','storage','requests-actions'
              ].includes(chunkInfo.name)
          ) {
            return 'sailor/[name].js'
          }
          return '[name].js'
        },
        format: 'es',
        inlineDynamicImports: false,
      },
    },
  },
  plugins: [copyStaticFiles()],
})