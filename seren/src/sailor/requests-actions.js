import {logger} from "../shared/logger-client.js";

if (!window.rc_requests) {
class RequestsActions {
  constructor() {
    this.abilities = window.abilities || null;
    this.elements = this.abilities.elements || null;
    this.instructionsStorage = window.storageModule?.instructionStorage || null;
  }

  async log(operation, message, level = 'log', details) {
    try {
      if (chrome && chrome.runtime && !chrome.runtime.lastError) {
        await chrome.runtime.sendMessage({ action: 'LOG', operation, message, level, details });
      }
    } catch (_) {
    }
  }
  async wait(seconds) {
    return window.clockModule?.instance?.wait(seconds);
  }
  async sendWelcomeMessage(roomId) {
    const welcomeMessages = ["Hey", "What's up?", "How's it going?", "Hi there","Hi?","Hi"]
    const text = welcomeMessages[Math.floor(Math.random() * welcomeMessages.length)]
    try {
      if (!this.instructionsStorage) {
        await this.log('Sailor.WelcomeMessage', 'instructionsStorage not available', 'error');
        return false;
      }
      const instruction = {
        type: 'sendMessage',
        room_id: roomId,
        content: {
          text: { value: text }
        },
        created_at: Date.now()
      };
      await this.instructionsStorage.addInstruction(instruction);
      await this.log('Sailor.WelcomeMessage', `Enqueued welcome message for room ${roomId}: ${text}`);
      return true;
    } catch (error) {
      await this.log('Sailor.WelcomeMessage', `Error enqueuing welcome message: ${error.message}`, 'error');
      return false;
    }
  }
  async clickRequestRoom() {
    try {
      this.abilities.scroll.toTop();
      await this.wait(0.5);
      try {
        const firstRequestView = this.elements.firstRequestView()
        const roomId = firstRequestView.attributes['room']?.value
        const acceptFirstRequests = this.elements.acceptFirstRequestButton()
        if (acceptFirstRequests)
        {
          acceptFirstRequests.click()
          return roomId
        }  
      }
      catch (err){
      }
      const firstRoom = this.elements.virtualScrollScroller()?.querySelector('rs-rooms-nav-room');
      if (!firstRoom) {
        await this.log('Sailor.ChooseRequestToAccept', 'No rooms found in the virtual scroller', 'warn');
        return null;
      }
      
      const roomId = firstRoom.getAttribute('name') || firstRoom.getAttribute('room');
      if (!roomId) {
        await this.log('Sailor.ChooseRequestToAccept', 'No room ID found on the first room element', 'warn');
        return null;
      }
      
      await this.log('Sailor.ChooseRequestToAccept', `Found first room with ID: ${roomId}`, 'info');
      const navButton = firstRoom.shadowRoot?.querySelector('a');
      if (!navButton) {
        await this.log('Sailor.ChooseRequestToAccept', 'Navigation button not found in the first room', 'warn');
        return roomId; // Still return roomId even if navigation fails, as the room might still be accessible
      }
      
      navButton.click();
      return roomId
    } catch (error) {
      await this.log('Sailor.ClickFirstRoom', `Error clicking first room: ${error.message}`, 'error');
      return null;
    }
  }
  async goToRequestsPage() {
    try {
      await this.goBackToMain()
      const requestsButton = this.elements.requestsButton();
      const badge = requestsButton.querySelector('rs-notifications-badge');
      const count = badge ? parseInt(badge.getAttribute('count') || '0', 10) : 0;
      if (count> 0) {
        requestsButton.querySelector('div[tabindex="0"]')?.click();
      }
      else
      {
        await this.log('Sailor.AcceptInvites', `All requests is accepted`, 'warn',error);
      }
      return count
    }
    catch (error) {
      await this.log('Sailor.AcceptInvites', `No Requests to accept on the page`, 'warn',error);
      return 0
    }
  }
  async acceptOrIgnoreRequest(roomId,needAccept=true)
  {
    
    try {
      let roomOverlay = this.elements.roomOverlay()
      await this.wait(1)
      const roomInviteControl = roomOverlay.querySelector('rs-room-invite').shadowRoot
      const buttons = roomInviteControl.querySelector('.buttons')
      const buttonPosition = needAccept ? 2 : 1
      const button = buttons.querySelectorAll('button')[buttonPosition]
      if (button) {
        button.click()
      }
      else{
        await this.log('Sailor.AcceptInvites','No button for click found','warn')
      }
      return true
    }
    catch (acceptError) {
      await this.log('Sailor.ChooseRequestToAccept', `Error clicking accept button: ${acceptError.message}`, 'warn');
      return false
    }
  }
  async selectRequest() {
    this.elements.requestsView()
  }
  
  async getFirstRequestRoom(serenName) {
    try {
      const reqCount = await this.goToRequestsPage()
      if (!reqCount){
        return { success: false, error: 'Requests button not found or No requests' };
      }
      await this.wait(0.5);
      const roomId = await this.clickRequestRoom()
      
      if (!roomId) {
        return { success: false, error: 'No requests found' };
      }
      await this.wait(1);
      const roomInfo = await window.rc_rooms.query.get_peek_space_info(roomId,serenName)
      if (!roomInfo) {
        return { success: false, error: 'There are no room with this id in database' };
      }
      return roomInfo
    }
    catch (error) {
      await this.log('Sailor.ChooseRequestToAccept', `Error getting first request room: ${error.message}`, 'error');
      return false
    }
  }

  async goBackToMain(){
    try{
      const backButton = this.elements.backToMain()
      if (backButton) {
        backButton.click();
        await this.wait(0.5)
      }
    }
    catch (e) {

    }
  }
  async processInvites(roomInfo,needAccept=true) {
    try {
      if (!roomInfo) {
        return { success: false, error: 'No requests found' };
      }
      
      // if (roomInfo.events?.length != 0) {
      //   const message = roomInfo.messages[0]
      //   const userName = message?.user_name || message?.sender || 'Unknown user'
      //  // await this.log('Sailor.AcceptInvites', `"${userName}" room is flagged, be careful ! `, 'warn', roomInfo);
      // }
      await this.wait(1.5);
      const actionResult = await this.acceptOrIgnoreRequest(roomInfo.roomId,needAccept)
      if ( needAccept &&
          actionResult &&
          roomInfo.messages?.length == 0
      ) {
        await this.wait(2);
        await this.sendWelcomeMessage(roomInfo.roomId)
      }
     await this.goBackToMain()
      return {
        success: actionResult,
        roomId: roomInfo.room_id,
        roomData: roomInfo,
        message: actionResult?`Successfully accepted request`:`Could not process request`
      };
    } catch (error) {
      await this.log('Sailor.AcceptInvites', `Error accepting requests: ${error.message}`, 'error');
      await this.goBackToMain();
      return { success: false, error: error.message };
    }
  }
}
const requestActions = new RequestsActions()
window.rc_requests = { actions: requestActions };
}


