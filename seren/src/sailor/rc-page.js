if (!window.rc_page) {
  class RCPage {
    constructor() {
      this.pageReadyPromise = null
      this.SELECTORS = {
        app: 'body > rs-app, body > faceplate-app > rs-app',
        roomOverlay: 'div.container > rs-room-overlay-manager > rs-room',
        messageComposer: 'rs-message-composer',
        currentUser: 'rs-current-user',
      }
      this.lastCheckTime = 0
      this.checkInterval = 500
      this.maxWaitTime = 30000
      this.accountId = null
      this.isReady = false
      this.lastActivationAttempt = 0
      this.activationCooldown = 5000
    }

    log = async (operation, message, level = 'log',details) => {
      try {
        await chrome.runtime.sendMessage({ action: 'LOG', operation, message, level,details })
      } catch (_) {}
    }

    isPageReady = () => {
      try {
        const now = Date.now()
        if (now - this.lastCheckTime < this.checkInterval) {
          return this.isReady
        }
        this.lastCheckTime = now
        const app = document.querySelector(this.SELECTORS.app)
        if (!app || !app.shadowRoot) return false
        const room = app.shadowRoot.querySelector(this.SELECTORS.roomOverlay)
        if (!room || !room.shadowRoot) {
          // this.log('RC-Space', 'Room element not found or no shadowRoot', 'warn')
          this.isReady = true
          return true
        }
        const composer = room.shadowRoot.querySelector(this.SELECTORS.messageComposer)
        if (!composer || !composer.shadowRoot) {
         // this.log('RC-Space', 'Message composer not found or no shadowRoot', 'warn')
          this.isReady = true
          return true
        }
        this.isReady = true
        return true
      } catch (error) {
        this.isReady = false
        return false
      }
    }

    waitForPageReady = async (maxWaitTimeMs = this.maxWaitTime, checkIntervalMs = 500) => {
      if (this.pageReadyPromise) return this.pageReadyPromise
      this.pageReadyPromise = new Promise(async (resolve, reject) => {
        const startTime = Date.now()
        const checkPage = () => {
          if (this.isPageReady()) {
            this.log('RC-Space', 'Chat page ready', 'debug')
            resolve(true)
            return
          }
          if (Date.now() - startTime > maxWaitTimeMs) {
            this.log('RC-Space', 'Chat page failed to load within timeout', 'error')
            reject(new Error('Chat page activation timeout'))
            return
          }
          setTimeout(checkPage, checkIntervalMs)
        }
        checkPage()
      })
      return this.pageReadyPromise
    }

    getCurrentAccountId = () => {
      try {
        if (!this.isPageReady()) return null
        const app = document.querySelector(this.SELECTORS.app)
        const currAcc = app.querySelector(this.SELECTORS.currentUser)
        return currAcc?.attributes['display-name']?.value ?? null
      } catch (error) {
        return null
      }
    }

    activateChatPage = async () => {
      try {
        const now = Date.now()
        if (now - this.lastActivationAttempt < this.activationCooldown) {
          return {
            success: this.isReady,
            message: this.isReady ? 'Chat page already active' : 'Activation on cooldown',
            accountId: this.accountId,
          }
        }
        this.lastActivationAttempt = now
        await this.waitForPageReady()
        this.accountId = this.getCurrentAccountId() || 'Unknown'
        return {
          success: true,
          message: 'Chat page activated successfully',
          accountId: this.accountId,
        }
      } catch (error) {
        await this.log('RC-Space', `Failed to activate chat page: ${error.message}`, 'error')
        return { success: false, message: `Activation failed: ${error.message}` }
      }
    }

    resetPageReadyState = () => {
      this.pageReadyPromise = null
      this.lastCheckTime = 0
      this.isReady = false
      this.lastActivationAttempt = 0
    }

    reloadAndActivate = async () => {
      this.resetPageReadyState()
      location.reload()
      return new Promise(resolve => {
        const checkReadyState = () => {
          if (document.readyState === 'complete') {
            this.activateChatPage()
                .then(result => resolve(result))
                .catch(error => resolve({ success: false, message: error.message }))
          } else {
            setTimeout(checkReadyState, 500)
          }
        }
        setTimeout(checkReadyState, 1000)
      })
    }
  }

  window.rc_page = { RCPage: new RCPage() }
}

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (!window.rc_page?.RCPage) {
    sendResponse({ success: false, message: 'RsChatPage module not initialized' })
    return
  }
  const chatPage = window.rc_page.RCPage
  switch (request.action) {
    case 'CHECK_PAGE_READY':
      sendResponse({ isReady: chatPage.isPageReady(), accountId: chatPage.accountId })
      break
    case 'ACTIVATE_PAGE':
      chatPage.activateChatPage()
          .then(result => sendResponse(result))
          .catch(error => sendResponse({ success: false, error: error.message }))
      return true
    case 'RESET_PAGE_STATE':
      chatPage.resetPageReadyState()
      sendResponse({ success: true })
      break
    case 'RELOAD_AND_ACTIVATE':
      chatPage.reloadAndActivate()
          .then(result => sendResponse(result))
          .catch(error => sendResponse({ success: false, error: error.message }))
      return true
    case 'GET_ACCOUNT_ID':
      const accountId = chatPage.getCurrentAccountId()
      sendResponse({ success: !!accountId, accountId })
      break
  }
})