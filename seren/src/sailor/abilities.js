if (!window.abilities) {
  class RCElements {
    app = () =>
      document.querySelector('body > rs-app') ||
      document.querySelector('body > faceplate-app > rs-app')?.shadowRoot ||
      null

    roomsNav = () =>
      this.app()?.querySelector('rs-rooms-nav')?.shadowRoot ||
      null

    virtualScroll = () =>
      this.roomsNav()?.querySelector('rs-virtual-scroll') ||
      null

    virtualScrollScroller = () =>
      this.virtualScroll()?.shadowRoot ||
      null

    roomOverlay = () =>
      this.app()?.querySelector('div.container > rs-page-overlay-manager > rs-room')?.shadowRoot ||
      null

    firstRequestView = ()=>
        this.app()?.querySelector('div.container > rs-page-overlay-manager > rs-requests-view')?.shadowRoot.querySelector('rs-virtual-scroll')?.shadowRoot.querySelector('rs-roving-focus-wrapper >rs-requests-view-request')
    acceptFirstRequestButton = () =>
      this.firstRequestView()?.shadowRoot.querySelectorAll('div .button')[2]
    
    requestsView = () =>
        this.app()?.querySelectorAll('div.container > rs-page-overlay-manager > rs-requests-view > rs-requests-view-request')?.shadowRoot ||
        null
    composer = () =>
      this.roomOverlay()?.querySelector('rs-message-composer-old')?.shadowRoot ||
      null
    
    messageInput = () =>
      this.composer()?.querySelector('form > div')?.querySelector('input, textarea') ||
      null

    requestsButton = () =>
        this.roomsNav()?.querySelector('li[data-testid="requests-button"]') ||
        this.virtualScrollScroller()?.querySelector('li[data-testid="requests-button"]') ||
        null        

    submitButton=() =>
      this.composer()?.querySelector('form > button.button-send') ||
      null
    
    fileButton=() =>
      this.composer()?.querySelector('form > button:nth-child(1)') ||
      null
    
    roomList = () =>
      this.app()?.querySelector('rs-rooms-nav rs-virtual-scroll') ||
      null
    
    backToMain =()=>
        this.roomsNav()?.querySelector('div:nth-of-type(2) button') || null
    rooms = () =>
      this.app()?.querySelectorAll('rs-rooms-nav rs-virtual-scroll rs-rooms-nav-room') ||
      null
  }
  

  const elements = new RCElements()
  const abilities = {
    sleep: (ms) =>
        window.clockModule?.instance?.sleep(ms) || new Promise(resolve => setTimeout(resolve, ms)),
    rndm: (max) => Math.floor(Math.random() * max),
    elements: elements,
    scroll:{
      toTop:() => { elements.virtualScroll().scrollTop = 0}
    }, 
    read: {
      
    },
    navigate: {
      
    },
    type: {
      simulateTyping: async (inputElement, text) => {
        inputElement.focus();
        inputElement.dispatchEvent(new InputEvent('beforeinput', {
          inputType: 'insertText',
          data: text,
          bubbles: true
        }));
        inputElement.dispatchEvent(new CompositionEvent('compositionstart', { bubbles: true }));
        // human-like delay generator
        const humanDelay = (base = 80, variance = 40) => {
          const v = Math.max(0, (Math.random() * 2 - 1) * variance + base);
          return Math.round(v);
        };
        inputElement.value = ''
        for (let i = 0; i < text.length; i++) {
          const char = text[i];
          // fire compositionupdate every 5 characters
          if (i > 0 && i % 5 === 0) {
            inputElement.dispatchEvent(new CompositionEvent('compositionupdate', {
              data: text.slice(0, i),
              bubbles: true
            }));
          }
          const keyCode = char.charCodeAt(0);
          const events = [
            new KeyboardEvent('keydown', {
              key: char,
              code: `Key${char.toUpperCase()}`,
              keyCode,
              which: keyCode,
              bubbles: true,
              cancelable: true,
              composed: true
            }),
            new KeyboardEvent('keypress', {
              key: char,
              code: `Key${char.toUpperCase()}`,
              keyCode,
              which: keyCode,
              bubbles: true,
              cancelable: true,
              composed: true
            }),
            new KeyboardEvent('keyup', {
              key: char,
              code: `Key${char.toUpperCase()}`,
              keyCode,
              which: keyCode,
              bubbles: true,
              cancelable: true,
              composed: true
            }),
          ];
          inputElement.dispatchEvent(events[0]);
          inputElement.value += char;
          inputElement.dispatchEvent(new InputEvent('input', { bubbles: true }));
          inputElement.dispatchEvent(events[1]);
          await abilities.sleep(humanDelay());
          inputElement.dispatchEvent(events[2]);
          await abilities.sleep(humanDelay());
        }
        inputElement.dispatchEvent(new CompositionEvent('compositionend', { bubbles: true }));
        if (inputElement.value !== text) {
          inputElement.value = text;
          inputElement.dispatchEvent(new Event('input', { bubbles: true }));
          inputElement.dispatchEvent(new Event('change', { bubbles: true }));
        }
        // Retry up to 3 times to confirm exact match
        let attempts = 0;
        while (inputElement.value !== text && attempts < 2) {
          inputElement.value = text;
          inputElement.dispatchEvent(new Event('input', { bubbles: true }));
          inputElement.dispatchEvent(new Event('change', { bubbles: true }));
          await abilities.sleep(50);
          attempts++;
        }
        if (inputElement.value !== text) {
          console.warn('simulateTyping: final value mismatch', inputElement.value, text);
          return false;
        }
        
        // Simulate pressing Enter key
        const enterEvent = new KeyboardEvent('keydown', {
          key: 'Enter',
          code: 'Enter',
          keyCode: 13,
          which: 13,
          bubbles: true,
          cancelable: true,
          composed: true
        });
        inputElement.dispatchEvent(enterEvent);
        
        return true;
      },
    },

    media: {
      base64ToFile: (base64Data) => {
        const [header, data] = base64Data.split(',')
        const mimeType = header.split(':')[1].split(';')[0]
        const byteCharacters = atob(data)
        const byteArrays = []
        for (let offset = 0; offset < byteCharacters.length; offset += 512) {
          const slice = byteCharacters.slice(offset, offset + 512)
          const byteNumbers = new Array(slice.length)
          for (let i = 0; i < slice.length; i++) {
            byteNumbers[i] = slice.charCodeAt(i)
          }
          const byteArray = new Uint8Array(byteNumbers)
          byteArrays.push(byteArray)
        }
        const blob = new Blob(byteArrays, { type: mimeType })
        const extension = mimeType.split('/')[1] || 'file'
        return new File([blob], `file.${extension}`, { type: mimeType })
      },
    },

    attachments: {
      /**
       * Attach a file via a hidden <input type="file"> or selector.
       * @param {HTMLElement|string} inputElementOrSelector - File input element or CSS selector.
       * @param {File|string} file - File object or base64-encoded string.
       */
      attachFile: async (inputElementOrSelector, file) => {
        // Convert from base64 if necessary
        const fileObj = typeof file === 'string'
          ? abilities.media.base64ToFile(file)
          : file;
        // Prepare DataTransfer and assign to input
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(fileObj);
        const input = typeof inputElementOrSelector === 'string'
          ? document.querySelector(inputElementOrSelector)
          : inputElementOrSelector;
        input.files = dataTransfer.files;
        // Trigger change event to notify listeners
        input.dispatchEvent(new Event('input', { cancelable: true, bubbles: true, composed: true }));
        return true;
      },
    },

  }

  window.abilities = abilities
}