import { script<PERSON>oa<PERSON> } from './reef/script-loader.js'
import { StatusManager } from './reef/status-manager.js'
import { SerenConfig } from './seren/seren-conf.js'
import { LogMonitor } from './shared/log-monitor.js'
import { logger } from './shared/logger-client.js'
import {FIVE_MINUTES, STORAGE_KEYS} from './shared/constants.js'
import { Seren } from './seren.js'
import { ReefInstaller } from './reef/installer.js'
import {DuxConnector} from "./reef/dux-connector.js";
import { InstructionStorage } from './shared/storage.js'
import InstructionsProcessorSimular from './seren/instructionsProcessorSimular.js'

const statusManager = new StatusManager()
let seren = null
chrome.runtime.onInstalled.addListener(async details => {
    const oldLogs = (await chrome.storage.local.get("reef.chronicles"))["reef.chronicles"] ?? []
    const newLogs = [...oldLogs, {
        timestamp: Date.now(),
        operation: 'Service',
        message: `Reef v${chrome.runtime.getManifest().version} has been installed or updated`,
        level: 'info',
        details: {
            startTime: new Date().toLocaleString(),
            version: chrome.runtime.getManifest().version
        }
    }]
    const serenConfig = new SerenConfig();
    await serenConfig.update({})

    await chrome.storage.local.set({ "seren.maxInvites": 40 })
    await chrome.storage.local.set({ "acceptedInvitations": [] })
    
    await chrome.storage.local.set({ "reef.chronicles": newLogs })
    // const defaultConfig = new SerenConfig()
    // await chrome.storage.local.set({serenConfig: defaultConfig.getAll()})
    if (chrome.declarativeContent) {
        chrome.declarativeContent.onPageChanged.removeRules(undefined, () => {
            chrome.declarativeContent.onPageChanged.addRules([
                {
                    conditions: [
                        new chrome.declarativeContent.PageStateMatcher({
                            pageUrl: {hostSuffix: 'reddit.com', pathContains: '/chat/'}
                        })
                    ],
                    actions: [new chrome.declarativeContent.ShowAction()]
                }
            ])
        })
    }
})
function initializeServices() {
    scriptLoader.setupListeners()

    seren = new Seren(true)
    logger('Reef.Initialization', 'Background service started successfully')
    statusManager.updateStatus(seren)
    setupLogMonitoringAlarm()
    chrome.storage.local.get(['autoacceptInvites', 'seren.maxInvites', 'acceptedInvitations'], r => {
        if (r.autoacceptInvites === undefined || r.autoacceptInvites == true) chrome.storage.local.set({autoacceptInvites: false})
        if (r['seren.maxInvites'] === undefined) chrome.storage.local.set({"seren.maxInvites": 40})
        if (r.acceptedInvitations === undefined) chrome.storage.local.set({acceptedInvitations: []})
    })
}

function setupLogMonitoringAlarm() {
    chrome.alarms.create('log-monitor-alarm', { periodInMinutes: 5, delayInMinutes: 1 })
    logger('LogMonitor', 'Log monitoring alarm set up to check every 5 minutes', 'debug')
}

initializeServices()
// new ReefInstaller().setupOnInstalled()

chrome.alarms.onAlarm.addListener(async alarm => {
    if (alarm.name === 'log-monitor-alarm') {
        await LogMonitor.handleAlarm(alarm, seren, logger)
    }
})


const handleAsyncMessage = async (operation, sendResponse, logContext = '') => {
    try {
        const result = await operation()
        sendResponse(result)
    } catch (error) {
        logger(logContext || 'MessageListener', `Error: ${error.message}`, 'error', { error })
        sendResponse({ error: error.message })
    }
}

const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms))
const LOG_STORAGE_KEY = 'reef.chronicles'
async function pushLogToStorage(entry) {
    let entries = []
    try {
        const result = await chrome.storage.local.get(LOG_STORAGE_KEY)
        if (result[LOG_STORAGE_KEY]) entries = result[LOG_STORAGE_KEY]
    } catch {}
    entries.push(entry)
    if (entries.length > 1000) entries.shift()
    try {
        await chrome.storage.local.set({ [LOG_STORAGE_KEY]: entries })
    } catch {}
}
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.type === 'log' && message.entry) pushLogToStorage(message.entry)


    if (message.type === 'SendMessage') {
        handleAsyncMessage(async () => {
            await sleep(1000)
            return { success: true }
        }, sendResponse, 'SendMessage')
    }
    return true
})
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    const handlers = {
        'GET_SERVICE_STATUS': async (request) => {
            const currentStatus = statusManager.getStatus()
            if (!request.forceRefresh && Date.now() - currentStatus.lastUpdated < 5000) {
                // Debug the current status
                //console.log('Returning cached status:', currentStatus);
                return currentStatus
            }
            const updatedStatus = await statusManager.updateStatus(seren);
            // Debug the updated status
            //console.log('Returning updated status:', updatedStatus);
            return updatedStatus;
        },

        'START_SERVICE': async (request) => {
            if (!seren.intervalId) {
                await statusManager.setServiceRunningState(true, seren)
            }
            return { status: 'Service started' }
        },

        'ENTER_SIMULATION_MODE': async (request) => {
            seren.instructionProcessor = new InstructionsProcessorSimular(null,seren.tabManager)
            if (!seren.intervalId) {
                await statusManager.setServiceRunningState(true, seren)
            }
            return { status: 'Simulation mode started' }
        },

        'STOP_SERVICE': async (request) => {
            await statusManager.setServiceRunningState(false, seren)
            await seren.tabManager.closeChatWindow()
            await chrome.storage.local.set({'autoacceptInvites':false})
            return { status: 'Service stopped' }
        },

        'ACTIVATE_CHAT_PAGE': async (request) => {
            const result = await seren.reloadAndActivateChatPage(true)
            logger('Reef.ActivateChatPage', `Chat page activated`,'info')
            return result
        },

        'SEND_LOGS': async (request) => {
            // Get user comment if provided
            const userComment = request.userComment || ''

            // Check if seren.account_name is null or undefined
            if (!seren.account_id) {
                // Add error record
                const timestamp = Date.now()
                const errorMessage = 'Cannot send logs: Seren name is unknown'

                logger('Reef.SendLogs', errorMessage, 'error', {
                    timestamp: new Date(timestamp).toISOString(),
                    details: 'The account_name is null or undefined. Please ensure Seren is properly configured.',
                    userComment: userComment
                })

                return {
                    success: false,
                    error: errorMessage
                }
            }

            // Log the action with user comment if provided
            const logMessage = userComment
                ? `Sending logs to server with comment: ${userComment}`
                : 'Sending logs to server'

            logger('Reef.SendLogs', logMessage, 'info', {
                userCommentProvided: !!userComment
            })

            const r=await chrome.storage.local.get(LOG_STORAGE_KEY);const e=r[LOG_STORAGE_KEY]||[];const h=['Timestamp','Formatted Time','Operation','Level','Message','Details'];const c=[h.join(',')];e.forEach(l=>{const t=new Date(l.timestamp).toLocaleTimeString([], {hour:'2-digit',minute:'2-digit',second:'2-digit'});const s=f=>{let d;if(f===null||f===undefined){d=''}else if(typeof f==='object'){try{d=JSON.stringify(f)}catch{d=String(f)}}else{d=String(f)};return d.includes(',')||d.includes('"')||d.includes('\n')?`"${d.replace(/"/g,'""')}"`:d};const row=[l.timestamp,t,s(l.operation),s(l.level),s(l.message),s(l.details)];c.push(row.join(','))});const logs=c.join('\n')
            // If account_name exists, proceed with sending logs
            await DuxConnector.sendLog(seren.config.duxEndpoint, seren.config.serenKey, seren.account_id, logs, userComment)

            // Log success with user comment information
            logger('Reef.SendLogs', 'Logs sent successfully', 'success', {
                timestamp: new Date().toISOString(),
                userCommentProvided: !!userComment
            })

            return { success: true }
        },
        'LOG': async (request) => {
            logger(request.operation, request.message, request.level,request.details)
            return { success: true }
        },

        'MANUAL_SERVICE_CHECK': async (request) => {
            if (seren.intervalId) {
                await seren.runServiceCycle()
                await statusManager.updateStatus(seren)
                return { success: true }
            }
            return { success: false, error: 'Not running' }
        },

        'MANUAL_INSTRUCTION_CHECK': async (request) => {
            if (seren.intervalId) {
                const tab = await seren.getRedditChatWindow();
                await this.cycles.processInstructions();
                return { success: true }
            }
            return { success: false, error: 'Not running' }
        },

        'FOCUS_CHAT_TAB': async (request) => {
            await seren.tabManager.activateTab(true)
            return result[0]?.result != null
        },

        'OPEN_CHAT_WINDOW': async (request) => {
            await seren.tabManager.activateTab(true)
            return { success: true }
        },

        'FIND_ROOM': async (request) => {
            await seren.tabManager.activateTab()
            const tabId = await seren.tabManager.currentTabId
            const result = await chrome.scripting.executeScript({
                target: { tabId: tabId },
                func: (roomName) => window.rc_actions?.ChatActions?.navigateToRoom(roomName),
                args: [request.roomName]
            })
            return {
                success: result[0]?.result === true,
                message: result[0]?.result === true ? 'Room found!' : 'Room not found'
            }
        },
        'PING': async (request) => true,
        'GET_AND_SEND_ROOM_OBSERVATIONS': async (request) => {
            const sentCount = await getAndSendRoomObservations(request.roomId, seren.config, request.numRecent)
            return {
                success: true,
                sentCount,
                message: `Sent ${sentCount} observation(s) from room ${request.roomId}`
            }
        },
        'CHECK_AND_SEND_ERROR_LOGS': async (request) => {
            // Check if Seren is running
            if (!seren.intervalId) {
                return { success: false, error: 'Cannot check logs: Seren is not running' }
            }

            if (!seren.account_id) {
                return { success: false, error: 'Cannot send logs: Seren name is unknown' }
            }

            const result = await (new LogMonitor()).checkAndSendErrorLogs(seren.config, seren.account_id)
            return result
        },

        ACCEPT_INVITES: async (request) => seren.acceptInvites(request)
    }

    if (handlers[request.action]) {
        handleAsyncMessage(() => handlers[request.action](request), sendResponse, request.action)
        return true
    }

    return ['GET_SERVICE_STATUS', 'MANUAL_SERVICE_CHECK',
        'MANUAL_INSTRUCTION_CHECK', 'RELOAD_AND_ACTIVATE_CHAT_PAGE',
        'CHECK_AND_SEND_ERROR_LOGS', 'GENERATE_TEST_ERROR_LOGS'].includes(request.action)
})

export async function sendObservationsToServer(config, messages, extraIds = []) {
    try {
        // First, retrieve the list of already sent message IDs
        const storage = await chrome.storage.local.get([STORAGE_KEYS.SENT_MESSAGES])
        const sentMessageIds = storage[STORAGE_KEYS.SENT_MESSAGES] || []

        // Filter out messages that have already been sent
        const newMessages = messages.filter(msg => !sentMessageIds.includes(msg.message_id))

        // If all messages were already sent, return early
        if (newMessages.length === 0) {
            logger('Reef.Observations', 'All messages were already sent, skipping', 'info')
            return 0
        }

        // Log how many messages were filtered out
        if (messages.length !== newMessages.length) {
            logger('Reef.Observations', `Filtered out ${messages.length - newMessages.length} already sent messages`, 'info')
        }


        // Only send messages if there are new ones to send
       const result = await DuxConnector.sendObservations(
            config.duxEndpoint,
            config.serenKey,
            newMessages
        )
        if (result && Array.isArray(result.blockedRoomIds)) {
            chrome.storage.local.get('blockedRoomIds', s => {
                const prev = Array.isArray(s.blockedRoomIds) ? s.blockedRoomIds : []
                const merged = [...new Set([...prev, ...result.blockedRoomIds])]
                chrome.storage.local.set({blockedRoomIds: merged})
            })
        }


        // Add the newly sent message IDs to the list
        const newIds = newMessages.map(msg => msg.message_id);
        newIds.push(...extraIds)
        const updatedSentMessageIds = [...newIds, ...sentMessageIds].slice(0, 1000)

        // Update storage with the new list
        await chrome.storage.local.set({
            [STORAGE_KEYS.SENT_MESSAGES]: updatedSentMessageIds
        })

        // Return the number of messages that were actually sent
        return newMessages.length
    } catch (error) {
        logger('Reef.Observations', `API error sending observations: ${error}`, 'error')
        throw error
    }
}

// Special version of sendObservationsToServer that always sends the latest message
// regardless of whether it was previously sent or not
export async function sendLatestMessageToServer(config, messages) {
    try {
        // Ensure all messages have valid text.value property
        const validatedMessages = messages.map(msg => {
            // Create a copy of the message to avoid modifying the original
            const validatedMsg = { ...msg };

            // Ensure content exists
            if (!validatedMsg.content) {
                validatedMsg.content = { type: 'text', text: { value: '' } };
            }

            // Ensure text exists
            if (!validatedMsg.content.text) {
                validatedMsg.content.text = { value: '' };
            }

            // Ensure text.value exists and is a string
            if (validatedMsg.content.text.value === undefined || validatedMsg.content.text.value === null) {
                validatedMsg.content.text.value = '';
            }

            return validatedMsg;
        });

        const response = await DuxConnector.sendObservations(
            config.duxEndpoint,
            config.serenKey,
            validatedMessages,
            true
        )
        
        // If server returned instructions, add them to the storage
        if (response.instructions && response.instructions.length > 0) {
            const instructionStorage = new InstructionStorage();
            
            // Format and store each instruction
            const formattedInstructions = response.instructions.map(instr => ({
                id: instr.id,
                room_id: instr.roomId,
                account_id: instr.serenId,
                time: new Date(instr.time).getTime(),
                sender_id: instr.senderId,
                sender_name: instr.senderName,
                social_media_channel: instr.socialMediaChannel,
                content: {
                    type: instr.content.type,
                    text: {
                        value: instr.content.text?.value,
                    },
                    media: instr.content.media
                }
            }));
            
            await instructionStorage.addInstructions(formattedInstructions, {resetProcessed: true});
            console.log(`Added ${formattedInstructions.length} instructions from server response`);
        }
        
        const storage = await chrome.storage.local.get([STORAGE_KEYS.SENT_MESSAGES])
        let sentMessageIds = storage[STORAGE_KEYS.SENT_MESSAGES] || []

        const newIds = messages.map(msg => msg.message_id)
        sentMessageIds = [...newIds, ...sentMessageIds].slice(0, 1000)

        await chrome.storage.local.set({
            [STORAGE_KEYS.SENT_MESSAGES]: sentMessageIds
        })
        logger('Reef.Observations', `Sent latest message (bypassing duplicate check)`, 'info', response)
        if (response.blockedRoomIds?.length > 0) {
            logger('Reef.Observations', `Room is blocked, do not expect response`, 'warn', response.blockedRoomIds)
        }
        return messages.length
    } catch (error) {
        logger('Reef.Observations', `API error sending latest message: ${error}`, 'error')
        throw error
    }
}

async function getAndSendRoomObservations(roomId, config, numRecent = 0) {
    try {
        const tab = await seren.tabManager.getRedditChatWindow()
        
        const result = await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            func: (threshold, roomId, numRecent) => {
                const rcRooms = window.rc_rooms || (window.opener && window.opener.rc_rooms);
                if (!rcRooms?.RoomsQuery) {
                    throw new Error('RoomsQuery not found');
                }
                const query = new rcRooms.RoomsQuery();
                return query.get_space_data(threshold, roomId, numRecent);
            },
            args: [FIVE_MINUTES * 10, roomId, numRecent]
        });

        if (!result || !result[0]?.result) {
            throw new Error('Failed to get observations');
        }
        
        const observations = result[0].result;

        if (!observations?.messages?.length) {
            logger('Reef.RoomObservations', `No messages found in room ${roomId}. Sending Welcome message`, 'warn');
            const result = await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                func: () => window.rc_requests.actions?.sendWelcomeMessage(),
            });
            return 0
        }

        // Take only the latest message
        const latestMessage = observations.messages[observations.messages.length - 1]

        // Ensure the latest message has a valid text.value property
        if (!latestMessage.content) {
            latestMessage.content = { type: 'text', text: { value: '' } }
        } else if (!latestMessage.content.text) {
            latestMessage.content.text = { value: '' }
        } else if (latestMessage.content.text.value === undefined || latestMessage.content.text.value === null) {
            latestMessage.content.text.value = ''
        }

        // Create an array with just the latest message
        const messagesToSend = [latestMessage]

        // Call a special version of sendObservationsToServer that bypasses the duplicate check
        // for this specific use case where we always want to send the latest message
        const sentCount = await sendLatestMessageToServer(config, messagesToSend)
        logger('Reef.RoomObservations',
            `Sent observation from ${latestMessage.user_name}: "${latestMessage.message_text}"`,
            'success',
            latestMessage
        )

        return sentCount

    } catch (error) {
        logger('Reef.RoomObservations', `Error getting/sending room observations: ${error}`, 'error')
        throw error
    }
}

// Set up the alarm listener for log monitoring
chrome.alarms.onAlarm.addListener(async (alarm) => {
    if (alarm.name === 'log-monitor-alarm') {
        await LogMonitor.handleAlarm(alarm, seren, logger)
    }
})
