import { logger } from '../shared/logger-client.js'
import { scriptLoader } from './script-loader'


export class RedditTabManager {
  constructor() {
    this.currentChatTabId = null
  }
  
  async runScriptSync(func){
    return await chrome.scripting.executeScript({
      target: { tabId: this.currentChatTabId },
      func: () => {
        return func()
      }
    });
  }
  

  async activateTab(force = false, tab = null) {
    await this.wakeUpTab(tab)
    
    tab = tab || await this.getRedditChatWindow()
    if (!tab) return null
    
    try {
      this.currentTabId = tab.id
      this.currentWindowId = tab.windowId
      // Only focus/activate if forced or tab is not already focused/active
      if (!tab.active)
      {
        await chrome.tabs.update(tab.id, { active: true })
      }
      if (force) {
        await chrome.tabs.update(tab.id, { active: true })
        //await chrome.windows.update(tab.windowId, { focused: true })
      }
      return tab
    } catch (error) {
      logger('RC-Space.Error', `Error activating tab: ${error}`, 'error')
      return tab
    }
  }
  
  async wakeUpTab(tab = null) {
    tab = tab || await this.getRedditChatWindow()
    if (!tab) return null
    
    try {
      this.currentTabId = tab.id
      await chrome.scripting.executeScript({
        target: { tabId: this.currentTabId },
        func: () => {
          if (window.rc_page?.RCPage) {
            return window.rc_page.RCPage.resetPageReadyState()
          }
        }
      })
      
      return tab
    } catch (error) {
      logger('RC-Space.Error', `Error waking up tab: ${error}`, 'error')
      return null
    }
  }
  updateActiveTabs = async () => {
    try {
      const tabs = await chrome.tabs.query({ url: '*://*.reddit.com/*' })
      if (chrome.runtime.lastError) throw chrome.runtime.lastError

      const chatTabs = tabs.filter(tab => tab.url.startsWith('https://chat.reddit.com/')).sort((a, b) => b.active - a.active)

      if (chatTabs.length > 1) {
        for (let i = 1; i < chatTabs.length; i++) {
          await chrome.tabs.remove(chatTabs[i].id)
          const tabId = chatTabs[i].id
          chatTabs.splice(i, 1)
          logger('RC-Space.Warn', `Closed duplicate chat page ${tabId}`, 'warn')
        }
      }

      if (chatTabs.length === 1) {
        if (this.currentChatTabId !== chatTabs[0].id) {
          await scriptLoader.loadScripts(chatTabs[0].id)
          this.currentChatTabId = chatTabs[0].id
        }
        
      } else {
        this.currentChatTabId = null
      }

      return chatTabs.length > 0
    } catch (error) {
      logger('RC-Space.Error', `Error tracking tabs: ${error}`, 'error')
      return false
    }
  }

  getRedditChatWindow = async (createIfNotExists = true) => {
    try {
      await this.updateActiveTabs()
      if (this.currentChatTabId) {
        const tab = await chrome.tabs.get(this.currentChatTabId)
        if (!chrome.runtime.lastError && tab) {
          return tab
        }
      }

      if (createIfNotExists) {
        logger('RC-Space.Create', 'Creating new chat page', 'info')
        return await this.createNewChatWindow()
      }

      logger('RC-Space.Error', 'No active chat tab found', 'error')
      return null
    } catch (error) {
      logger('RC-Space.Error', `Error getting chat tab: ${error}`, 'error')
      return null
    }
  }

  closeChatWindow = async () => {
    try {
      if (this.currentChatTabId) {
        await chrome.tabs.remove(this.currentChatTabId)
        this.currentChatTabId = null
      }
    } catch (error) {
      logger('RC-Space.Error', `Error closing chat tab: ${error}`, 'error')
    }
  }


  createNewChatWindow = async () => {
    try {
      const newTab = await chrome.tabs.create({ url: 'https://chat.reddit.com/' })
      if (chrome.runtime.lastError) throw chrome.runtime.lastError

      this.currentChatTabId = newTab.id
      await scriptLoader.loadScripts(newTab.id)
      const result = await chrome.scripting.executeScript({
        target: { tabId: this.currentChatTabId },
        func: () => {
          if (window.rc_page?.RCPage) {
              window.rc_page.RCPage.resetPageReadyState()
              return window.rc_page.RCPage.activateChatPage()
        }}}
      );
      return newTab
    } catch (error) {
      logger('RC-Space.Create', `Error creating new chat page: ${error}`, 'error')
      return null
    }
  }

  handleTabUpdate = async (tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url.includes('reddit.com')) {
      await this.updateActiveTabs();
      await scriptLoader.loadScripts(tabId)
    }
  }

  handleTabCreate = async (tab) => {
    await this.updateActiveTabs();
    if (tab.url.startsWith('https://chat.reddit.com/') && this.currentChatTabId !== tab.id) {
      await chrome.tabs.remove(tab.id);
      logger('RC-Space.Destroy', `Closed uncontrolled reddit chat page ${tab.id}`, 'warn');
    }
  }
}