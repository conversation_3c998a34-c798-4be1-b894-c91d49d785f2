import { logHistory } from '../shared/logger.js'
import {IconManager} from "./icon-manager.js";

export class StatusManager {
    statusCacheInterval = null
    async setServiceRunningState(isRunning, serenInstance) {
        try {
            const scheduledTimes = await serenInstance.getNextScheduledTimes()
            await this.updateStatus({
                ...serenInstance,
                getServiceStatusWithOperations: () => ({
                    status: isRunning ? 'starting' : 'stopping',
                    startTime: scheduledTimes.startTime,
                    stopTime: scheduledTimes.stopTime,
                    runningTime: scheduledTimes.runningTime,
                    stoppedTime: scheduledTimes.stoppedTime
                }),
                getNextScheduledTimes: () => scheduledTimes
            })
            if (isRunning) {
                await serenInstance.run()
                if (!this.statusCacheInterval) {
                    this.startStatusCacheUpdates(serenInstance)
                }
            } else {
                serenInstance.stop()
                if (this.statusCacheInterval) {
                    clearInterval(this.statusCacheInterval)
                    this.statusCacheInterval = null
                }
            }
            const updatedScheduledTimes = await serenInstance.getNextScheduledTimes()
            await this.updateStatus({
                ...serenInstance,
                getServiceStatusWithOperations: () => ({
                    status: isRunning ? 'running' : 'stopped',
                    startTime: updatedScheduledTimes.startTime,
                    stopTime: updatedScheduledTimes.stopTime,
                    runningTime: updatedScheduledTimes.runningTime,
                    stoppedTime: updatedScheduledTimes.stoppedTime
                }),
                getNextScheduledTimes: () => updatedScheduledTimes
            })
        } catch (error) {
            await this.updateStatus({
                ...serenInstance,
                getServiceStatusWithOperations: () => ({ status: 'stopped' }),
                getNextScheduledTimes: () => ({ nextServiceCheck: null, nextInstructionCheck: null })
            })
            throw error
        }
    }
    startStatusCacheUpdates(serenInstance) {
        if (this.statusCacheInterval) return
        this.statusCacheInterval = setInterval(() => {
            this.updateStatus(serenInstance)
        }, 1000)
    }
    constructor() {
        this.cache = {
            status: 'stopped',
            currentOperation: null,
            nextServiceCheck: null,
            nextInstructionCheck: null,
            logs: [],
            lastUpdated: Date.now(),
            lastLogUpdate: Date.now(),
        }
        this.listeners = new Set()
        this.iconManager = new IconManager()

    }

    updateIconState = async (status) => {
        if (!this.iconManager) return
        switch (status) {
            case 'running':
                await this.iconManager.setActiveIcon()
                break
            case 'stopped':
                await this.iconManager.setInactiveIcon()
                break
            case 'starting':
            case 'stopping':
                break
        }
    }

    async getChatStatus() {
        try {
            const tabs = await chrome.tabs.query({ url: "*://chat.reddit.com/*" });
            const chatTab = tabs[0];

            if (chatTab) {
                const result = await chrome.scripting.executeScript({
                    target: { tabId: chatTab.id },
                    func: () => ({
                        isReady: window.rc_page?.RCPage?.isPageReady() || false,
                        accountId: window.rc_page?.RCPage?.getCurrentAccountId() || null
                    })
                });
                return { hasTab: true, ...result[0]?.result };
            }
            return { hasTab: false, isReady: false };
        } catch (error) {
            return { hasTab: false, isReady: false, error: error.message };
        }
    }
    updateStatus = async (serenInstance) => {
        if (!serenInstance) return this.cache
        const status = await serenInstance.getServiceStatusWithOperations()
        const scheduledTimes = await serenInstance.getNextScheduledTimes()
        const logs = logHistory ? logHistory.getRecentLogs(100) : []
        this.cache = {
            ...status,
            nextServiceCheck: scheduledTimes.nextServiceCheck,
            nextInstructionCheck: scheduledTimes.nextInstructionCheck,
            logs,
            lastUpdated: Date.now(),
            lastLogUpdate: Date.now(),
        }
        await this.updateIconState(this.cache.status)
        this.notifyStatusListeners()
        return this.cache
    }


    notifyStatusListeners = () => {
        this.listeners.forEach(callback => {
            try {
                callback(this.cache)
            } catch (error) {
                console.error('Error in status listener:', error)
            }
        })
    }

    getStatus = () => this.cache
}