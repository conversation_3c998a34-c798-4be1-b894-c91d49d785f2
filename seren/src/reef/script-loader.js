import { logger } from '../shared/logger-client.js'


class ScriptLoader {
  constructor() {
    this.loadedScripts = {}
    this.loadPromises = {}
    this.SCRIPT_FILES = ['/sailor/storage.js', '/sailor/page-clock.js','/sailor/rc-page.js','/sailor/rc-rooms.js','/sailor/abilities.js','/sailor/actions.js', '/sailor/requests-actions.js']
  }

  isScriptLoaded = (tabId) => !!this.loadedScripts[tabId]

  loadScripts = async (tabId) => {
    if (this.loadedScripts[tabId]) {
      return { status: 'already-loaded' }
    }
    if (this.loadPromises[tabId]) {
      return this.loadPromises[tabId]
    }
    this.loadPromises[tabId] = (async () => {
      try {
        const results = await chrome.scripting.executeScript({
          target: { tabId },
          files: this.SCRIPT_FILES,
        })
        this.loadedScripts[tabId] = true
        return { status: 'loaded', results }
      } finally {
        delete this.loadPromises[tabId]
      }
    })()
    return this.loadPromises[tabId]
  }

  resetLoadStatus = (tabId) => {
    delete this.loadedScripts[tabId]
    delete this.loadPromises[tabId]
  }

  setScriptLoaded = (tabId, status) => {
    this.loadedScripts[tabId] = status
  }

  handleTabUpdated = (tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url && tab.url.includes('reddit.com')) {
      this.resetLoadStatus(tabId)
      this.loadScripts(tabId).catch(err => {
        logger('ScriptLoader', `Error on auto-loading scripts for tab ${tabId}: ${err}`, 'error')
      })
    }
  }

  handleTabRemoved = (tabId) => {
    this.resetLoadStatus(tabId)
  }

  setupListeners = () => {
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this.handleTabUpdated(tabId, changeInfo, tab)
    })
    chrome.tabs.onRemoved.addListener((tabId) => {
      this.handleTabRemoved(tabId)
    })
  }
}

export const scriptLoader = new ScriptLoader()