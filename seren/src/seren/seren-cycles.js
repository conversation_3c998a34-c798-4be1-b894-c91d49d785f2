import {logger} from '../shared/logger-client.js'
import {SAFETY_MARGIN, STORAGE_KEYS} from '../shared/constants'
import {sendObservationsToServer} from '../reef.js'
import {InstructionStorage} from '../shared/storage.js'
import {DuxConnector} from '../reef/dux-connector.js'

export class SerenCycles {
  async _executeSafely(asyncFn, logContext, { rethrow = false, defaultReturnValue = null, onErrorCallback = null, logSeverity = 'error' } = {}) {
    try {
      return await asyncFn();
    } catch (error) {
      logger(logContext, `Error: ${error.message || error}`, logSeverity, { error });
      if (onErrorCallback) {
        await onErrorCallback(error); // Ensure onErrorCallback can be async
      }
      if (rethrow) {
        throw error;
      }
      return defaultReturnValue;
    }
  }

  constructor(seren) {
    this.seren = seren
    this.cycleCount = 0
    this.cycleQueue = []
    this.isRunningCycle = false
    this.currentCycleName = null
    this.maxQueueSize = 10
    this.isProcessingQueue = false
  }

  generateCycleName(name = 'wave') {
    const now = new Date();
    const timeStr = now.toTimeString().slice(0,5).replace(':', '-');
    const cycleName = `${name}:${this.cycleCount}-${timeStr}`;
    return cycleName || `wave:${this.cycleCount}-${Date.now()}`;
  }
  async enqueueCycle() {
    const cycleName = this.generateCycleName();

    if (this.cycleQueue.length >= this.maxQueueSize) {
      logger('Cascade', `Queue full (${this.maxQueueSize}), dropping oldest cycle`, 'warn');
      this.cycleQueue.shift();
    }

    this.cycleQueue.push(cycleName);
    if (this.cycleQueue.length > 1) {
      logger('Cascade', `${cycleName} added to Cascade. \t\t\t\t\t\t COUNT: ${this.cycleQueue.length}`, 'info');
    }
    await this.processQueue();
    return cycleName;
  }

  async processQueue() {
    if (this.isProcessingQueue) return;

    this.isProcessingQueue = true;
    try {
      while (this.cycleQueue.length > 0 && !this.isRunningCycle) {
        await this._executeSafely(async () => {
          if (this.isRunningCycle || !this.cycleQueue.length) return;
          this.isRunningCycle = true;
          this.currentCycleName = this.cycleQueue.shift();
          this.cycleStartTime = Date.now();
          logger(`Wave`, `Starting ${this.currentCycleName}`, 'wave');
          const waveStatus = await this.wave();
          if (waveStatus) {
            logger(`Wave`, `Completed ${this.currentCycleName}`, 'success');
          } else {
            logger(`Wave`, `Failed ${this.currentCycleName}`, 'error');
          }
          this.isRunningCycle = false;
        }, 'Wave.ProcessQueue', {
          onErrorCallback: async () => {
            this.isRunningCycle = false;
          }
        });
      }
    } finally {
      this.isProcessingQueue = false;
    }
  }

  async runCycle(name = 'wave') {
    this.cycleCount++;
    this.seren.lastRunTime = Date.now();
    return await this.enqueueCycle(name);
  }

  async getObservations(room_id = null) {
    return this._executeSafely(async () => {
      const tab = await this.seren.tabManager.getRedditChatWindow();
      const storage = await chrome.storage.local.get([STORAGE_KEYS.LAST_CHECK_TIME]);
      const lastCheckTime = storage[STORAGE_KEYS.LAST_CHECK_TIME];
      const threshold = lastCheckTime
        ? lastCheckTime - SAFETY_MARGIN
        : Date.now() - this.seren.config.observationTreshold;

      const injectionResults = await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: async (threshold, roomId) => {
          try {
            await window.rc_rooms.query.cache.updateCache();
            return await window.rc_rooms.query.get_space_data(threshold, roomId, 0);
          } catch (e) {
            return { error: e.message || 'Unknown error in injected script' };
          }
        },
        args: [threshold, room_id],
      });

      if (chrome.runtime.lastError) {
        throw new Error(chrome.runtime.lastError.message);
      }

      const mainResult = injectionResults[0].result;
      if (mainResult && mainResult.error) {
        throw new Error(mainResult.error);
      }

      return mainResult;
    }, 'GetObservations', { rethrow: true });
  }
  async sendObservations(observations) {
    if (!observations?.messages?.length) return 0;

    return this._executeSafely(async () => {
      await this.seren.tabManager.activateTab();
      const storage = await chrome.storage.local.get([STORAGE_KEYS.SENT_MESSAGES]);
      let sentMessageIds = storage[STORAGE_KEYS.SENT_MESSAGES] || [];

      const newMessages = observations.messages.filter(msg => !sentMessageIds.includes(msg.message_id));

      if (newMessages.length === 0) return 0;

      const sentCount = await this._executeSafely(async () => {
        return await sendObservationsToServer(this.seren.config, newMessages);
      }, 'Seren.Observations.API', { 
        rethrow: true, 
        onErrorCallback: (apiError) => { 
          throw new Error(`API error stopped wave execution: ${apiError.message || apiError}`); 
        }
      });

      logger('Seren.Observations', `Shipped ${newMessages.length} new observation${newMessages.length === 1 ? '' : 's'} ✨`, 'info', {
        messages: newMessages,
        total: newMessages.length
      });
      return sentCount;
    }, 'Seren.Observations.Main', { rethrow: true });
  }
  async processObservations() {
    const observations = await this.getObservations()
    const observationsCount = observations?.messages?.length || 0
    const sentCount = await this.sendObservations(observations)
  }

  async processRooms(maxRooms = 10) {
    try {
      // Fetch active rooms
      let activeRooms = [];
      try {
        activeRooms = await this.seren.checkActiveRooms();
        
      } catch (roomsError) {
        logger('Seren.Rooms', `Error fetching active rooms: ${roomsError.message}`, 'error', { error: roomsError });
        return;
      }

      // Retrieve last invite time
      let lastInviteTime = null;
      try {
        const result = await chrome.storage.local.get('lastInviteTime');
        lastInviteTime = result.lastInviteTime;
      } catch (timeError) {
        logger('Seren.Rooms', `Error retrieving lastInviteTime: ${timeError.message}`, 'error', { error: timeError });
      }

      // Cooldown check: skip if within 5 minutes
      if (lastInviteTime) {
        const now = Date.now();
        const fiveMinutesAgo = now - (4 * 60 * 1000);
        if (new Date(lastInviteTime).getTime() > fiveMinutesAgo) {
          const remaining = Math.ceil((new Date(lastInviteTime).getTime() + 4 * 60 * 1000 - now) / 1000);
          const minutes = Math.floor(remaining / 60);
          const seconds = remaining % 60;
          logger('Seren.Rooms', `Skipping invite acceptance: cooldown active (${minutes}m ${seconds}s remaining)`, 'info');
          return;
        }
      }

      // Retrieve blocked rooms
      let blockedRoomIds = [];
      try {
        const blocked = await chrome.storage.local.get(['blockedRoomIds']);
        blockedRoomIds = blocked.blockedRoomIds || [];
      } catch (blockedError) {
        logger('Seren.Rooms', `Error retrieving blockedRooms: ${blockedError.message}`, 'error', { error: blockedError });
      }

      // Filter out blocked rooms
      let filteredRooms = activeRooms.filter(room => !blockedRoomIds.includes(room.sender_name));

      // Check statuses with DuxConnector
      try {
        const senderNames = filteredRooms.map(r => r.sender_name);
        if (senderNames.length) {
          const statuses = await DuxConnector.checkFishBulk(
              this.seren.config.duxEndpoint,
              this.seren.config.serenKey,
              senderNames
          );
          const toBlock = statuses.filter(s => s.status >= 1).map(s => s.name);
          if (toBlock.length) {
            const updatedBlocked = Array.from(new Set([...blockedRoomIds, ...toBlock]));
            await chrome.storage.local.set({ blockedRoomIds: updatedBlocked });
            blockedRoomIds = updatedBlocked;
          }
          const allowed = statuses.filter(s => s.status < 1).map(s => s.name);
          filteredRooms = filteredRooms.filter(room => allowed.includes(room.sender_name));
        }
      } catch (statusError) {
        logger('Seren.Rooms', `Error checking room statuses: ${statusError.message}`, 'error', { error: statusError });
      }

      // Calculate available slots
      const activeChats = filteredRooms.length;
      const availableSlots = maxRooms - activeChats;
      if (availableSlots <= 0) return;

      // Determine invites to accept
      const invitesToAccept = availableSlots >= 3
          ? Math.floor(Math.random() * 2) + 2
          : availableSlots;

      logger('Seren.Rooms', `Stats: Open ${filteredRooms.length} Active ${activeRooms.length} `, 'info', { activeRooms, filteredRooms });
      if (invitesToAccept > 0) {
        try {
          logger('Seren.Rooms', `Plan to accept ${invitesToAccept} invites`, 'info', { invitesToAccept });
          
          await this.seren.acceptRequestHandler.acceptInvites({ count: invitesToAccept });
        } catch (acceptError) {
          logger('Seren.Rooms', `Error accepting invites: ${acceptError.message}`, 'error', { error: acceptError });
        }
      }
    } catch (unexpectedError) {
      logger('Seren.Rooms', `Unexpected error: ${unexpectedError.message}`, 'error', { error: unexpectedError });
    }
  }

  /**
   * Check auto-accept settings and invite limits, then invoke processRooms().
   */
  async handleRoomInvites() {
    try {
      const result = await chrome.storage.local.get([
        'seren.maxInvites',
        'autoacceptInvites',
        'acceptedInvitations'
      ]);
      if (!result.autoacceptInvites) {
        logger('Wave.Rooms','Manual accept invites mode enabled', 'info')
        return;
      }
      const maxInvites = result['seren.maxInvites'] || 0;
      const acceptedInvitations = result['acceptedInvitations'] || [];
      const now = Date.now();
      const oneDayAgo = new Date(now - 24 * 60 * 60 * 1000);
      const todayInvites = acceptedInvitations.filter(
        invite => new Date(invite.timestamp) > oneDayAgo
      ).length;
      if (todayInvites > maxInvites) {
        logger('Wave.Rooms', `Invite limit reached (${todayInvites}/${maxInvites})`, 'info');
        return;
      }
      await this.processRooms();
    } catch (roomsError) {
      logger('Wave.Rooms', `Error in room handling: ${roomsError.message}`, 'error', { error: roomsError });
    }
  }

  async wave() {
    // this.seren.lockManager.acquireLock('message_processing');
    try {
      const storage = await chrome.storage.local.get(['activateFlag']);
      const activationValue = typeof storage.activateFlag === 'number' ? storage.activateFlag : 0;
      if (this.cycleCount % (10 * activationValue) === 0) {
        await chrome.windows.update(this.seren.tabManager.currentWindowId, { focused: true });
        logger('Seren.Wave', `Tab Focused: ${this.currentCycleName}`, 'success');
      }
      return await this._executeSafely(async () => {
        this.lastRunTime = Date.now();

        const obsSuccess = await this._executeSafely(
          async () => { await this.processObservations(); return true; },
          'Wave.Observations',
          { defaultReturnValue: false, logSeverity: 'warn' }
        );
        if (!obsSuccess) return false;

        const execSuccess = await this._executeSafely(
          async () => { await this.seren.executeInstructions(); return true; },
          'Wave.Execution',
          { defaultReturnValue: false }
        );
        if (!execSuccess) return false;

        const instructionStorage = new InstructionStorage();
        let processed = await this._executeSafely(
          async () => await instructionStorage.getProcessedInstructions(),
          'Wave.Instructions',
          { defaultReturnValue: [] }
        );

        if (processed.length) {
          const markSuccess = await this._executeSafely(
            async () => {
              await DuxConnector.markDelivered(
                this.seren.config.duxEndpoint,
                this.seren.config.serenKey,
                processed
              );
              return true;
            },
            'Wave.DuxConnector',
            { defaultReturnValue: false }
          );
          if (!markSuccess) return false;
        }

        await this.handleRoomInvites();
        return true;
      }, 'Wave.MainOperation', {
        defaultReturnValue: false,
        onErrorCallback: () => {
          this.seren.lastRunTime = Date.now();
        }
      });
    } finally {
      // this.seren.lockManager.releaseLock('message_processing');
    }
  }
}
