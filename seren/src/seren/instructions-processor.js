import {DuxConnector} from "../reef/dux-connector.js";
import {InstructionStorage, BackgroundMediaService} from "../shared/storage.js";

export class InstructionsProcessor {
    constructor(config, tabManager) {
        this.config = config;
        this.storage = new InstructionStorage();
        this.mediaService = new BackgroundMediaService();
        this.tabManager = tabManager;
    }
    async retrieveInstructions(serenName) {
        const lastReceivedTime = await this.storage.getLastReceivedTime();
        this.lastReceivedTime = lastReceivedTime ? new Date(lastReceivedTime).toISOString()
            : new Date((Date.now() - this.config.instructionsInterval)).toISOString();
        
        const instructions = await DuxConnector.readInstructions(
            this.config.duxEndpoint,
            this.config.serenKey,
            serenName,
            this.lastReceivedTime
        )
        await this.storage.setLastReceivedTime(Date.now());
        if (!instructions.length) {
            return;
        }
        await this.storage.addInstructions(instructions);
        
    }
    async executeInstructions() {
        const instructions = await this.storage.getUnprocessedInstructions();
        await this.storage.addToExecutionQueue(instructions);
        await chrome.scripting.executeScript({
            target: { tabId: this.tabManager.currentTabId },
            func: () => {
                window.rc_actions?.action.triggerQueue();
            }
        })
        return instructions.length;
    }
}