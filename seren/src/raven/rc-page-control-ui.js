
export class RCPageControlUI {
    constructor() {
        this.CHECK_INTERVAL = 3000;
        this.button = document.getElementById('openRedditChat');
        this.tackleBoxContainer = document.querySelector('.tackle-box-container');
        this.statusCheckInterval = null;
        this.inviteCounter =document.getElementById('inviteCounter');
        this.INVITE_CHECK_INTERVAL = 60000; // Check every minute

        if (this.button) {
            this.initialize();
        }
    }

    initialize() {
        this.button.style.display = 'flex';
        this.button.addEventListener('click', this.handleRedditChat.bind(this));

        // Initially hide the tackle box container
        if (this.tackleBoxContainer) {
            this.tackleBoxContainer.style.display = 'none';
            console.log('Initially hiding tackle box container');
        }

        this.updateButton();
        this.startStatusChecking();
       // this.initializeInviteCounter();
    }

    async handleRedditChat() {
        try {
            const tabs = await chrome.tabs.query({ url: "*://*.reddit.com/*", status: "complete" });
            const chatTabs = tabs.filter(tab => tab.url.includes("chat.reddit.com"));
            this.handleMultipleChatTabsWarning(chatTabs);

            const result = await this._handleRedditChat();
            this.updateButtonWithResult(result);
        } catch (error) {
            this.updateButtonWithResult({ success: false });
            throw error;
        }
    }

    handleMultipleChatTabsWarning(chatTabs) {
        if (chatTabs.length > 1) {
            const existingWarning = document.querySelector('.chat-warning');
            if (existingWarning) existingWarning.remove();

            const warningDiv = document.createElement('div');
            warningDiv.className = 'chat-warning';
            warningDiv.innerHTML = `
                <div class="warning-content">
                    ⚠️ Multiple chat tabs (${chatTabs.length})
                    <button class="close-warning">×</button>
                </div>
            `;

            this.button.parentNode.insertBefore(warningDiv, this.button.nextSibling);
            warningDiv.querySelector('.close-warning').addEventListener('click', () => warningDiv.remove());
        }
    }

    updateButtonWithResult(response) {
        if (!this.button) return;

        const statusElement = this.button.querySelector('.reddit-button__text');
        const iconElement = this.button.querySelector('.reddit-button__icon');
        if (!statusElement || !iconElement) return;

        if (response.success && response.accountId) {
            // Format account ID with newlines before capital letters
            statusElement.innerHTML = this.formatTextWithBreaks(response.accountId);
            this.button.classList.add('running-v3');
            iconElement.src = chrome.runtime.getURL('assets/reddit-sailor.png');
            console.log('Added running-v3 class to openRedditChat button');

            // Show the tackle box container
            if (this.tackleBoxContainer) {
                this.tackleBoxContainer.style.display = 'flex';
                console.log('Showing tackle box container');
            }
        } else if (response.success) {
            statusElement.innerHTML = 'Reddit';
            this.button.classList.add('running-v3');
            iconElement.src = 'https://www.redditstatic.com/shreddit/assets/favicon/180x180.png';
            console.log('Added running-v3 class to openRedditChat button');

            // Show the tackle box container
            if (this.tackleBoxContainer) {
                this.tackleBoxContainer.style.display = 'flex';
                console.log('Showing tackle box container');
            }
        } else {
            statusElement.innerHTML = 'Reddit';
            this.button.classList.remove('running-v3');
            iconElement.src = 'https://www.redditstatic.com/shreddit/assets/favicon/180x180.png';
            console.log('Removed running-v3 class from openRedditChat button');

            // Hide the tackle box container
            if (this.tackleBoxContainer) {
                this.tackleBoxContainer.style.display = 'none';
                console.log('Hiding tackle box container');
            }
        }
    }
    async getChatStatus() {
        try {
            const tabs = await chrome.tabs.query({ url: "*://chat.reddit.com/*" });
            const chatTab = tabs[0];

            if (chatTab) {
                const result = await chrome.scripting.executeScript({
                    target: { tabId: chatTab.id },
                    func: () => ({
                        isReady: window.rc_page?.RCPage?.isPageReady() || false,
                        accountId: window.rc_page?.RCPage?.getCurrentAccountId() || null
                    })
                });
                return { hasTab: true, ...result[0]?.result };
            }
            return { hasTab: false, isReady: false };
        } catch (error) {
            return { hasTab: false, isReady: false, error: error.message };
        }
    }
    async _handleRedditChat() {
        try {
            const isServiceAvailable = await chrome.runtime.sendMessage({ action: 'PING' }).catch(() => false);
            if (!isServiceAvailable) {
                await chrome.runtime.reload();
                throw new Error('Background service not responding. Extension reloaded, please try again.');
            }

            const response = await chrome.runtime.sendMessage({ action: 'ACTIVATE_CHAT_PAGE' });
            if (!response) throw new Error('No response from background service');

            return {
                success: response.success,
                accountId: response.accountId,
                message: response.message
            };
        } catch (error) {
            this.logError(error);
            throw error;
        }
    }
    async updateButton() {
        const status = await this.getChatStatus();
        if (!this.button) return;

        const iconElement = this.button.querySelector('.reddit-button__icon');
        const statusElement = this.button.querySelector('.reddit-button__text');
        if (!statusElement || !iconElement) return;

        if (status.isReady && status.accountId) {
            // Format account ID with newlines before capital letters
            statusElement.innerHTML = this.formatTextWithBreaks(status.accountId);
            this.button.classList.add('running-v3');
            iconElement.src = chrome.runtime.getURL('assets/reddit-sailor.png');
            console.log('Added running-v3 class to openRedditChat button (status update)');

            // Show the tackle box container
            if (this.tackleBoxContainer) {
                this.tackleBoxContainer.style.display = 'flex';
                console.log('Showing tackle box container (status update)');
            }
        } else if (status.isReady) {
            statusElement.innerHTML = 'Reddit';
            this.button.classList.add('running-v3');
            console.log('Added running-v3 class to openRedditChat button (status update)');

            // Show the tackle box container
            if (this.tackleBoxContainer) {
                this.tackleBoxContainer.style.display = 'flex';
                console.log('Showing tackle box container (status update)');
            }
        } else {
            statusElement.innerHTML = 'Reddit';
            this.button.classList.remove('running-v3');
            console.log('Removed running-v3 class from openRedditChat button (status update)');

            // Hide the tackle box container
            if (this.tackleBoxContainer) {
                this.tackleBoxContainer.style.display = 'none';
                console.log('Hiding tackle box container (status update)');
            }
        }
    }

    startStatusChecking() {
        if (this.statusCheckInterval) clearInterval(this.statusCheckInterval);
        this.statusCheckInterval = setInterval(() => this.updateButton(), this.CHECK_INTERVAL);
    }

    async countRecentInvites() {
        try {
            const result = await chrome.storage.local.get('acceptedInvitations');
            const invites = result.acceptedInvitations || [];
            const now = new Date();
            const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            
            return invites.filter(invite => {
                const inviteTime = new Date(invite.timestamp);
                return inviteTime > oneDayAgo;
            }).length;
        } catch (error) {
            console.error('Error counting invites:', error);
            return 0;
        }
    }

    async updateInviteCounter() {
        if (!this.inviteCounter) return;
        const count = await this.countRecentInvites();
        this.inviteCounter.textContent = count > 0 ? count : '0';
        this.inviteCounter.style.display = count > 0 ? 'flex' : 'none';
    }

    initializeInviteCounter() {
        if (this.inviteCounter) return;
        
        this.inviteCounter = document.createElement('div');
        this.inviteCounter.className = 'invite-counter';
        this.tackleBoxContainer.appendChild(this.inviteCounter);
        
        // Initial update
        this.updateInviteCounter();
        
        // Update periodically
        setInterval(() => this.updateInviteCounter(), this.INVITE_CHECK_INTERVAL);
    }

    destroy() {
        if (this.statusCheckInterval) clearInterval(this.statusCheckInterval);
        if (this.button) this.button.removeEventListener('click', this.handleRedditChat);
        if (this.inviteCounter) {
            this.inviteCounter.remove();
            this.inviteCounter = null;
        }
    }

    formatTextWithBreaks(text) {
        // Add breaks before capital letters (except the first character)
        if (!text) return 'Red<br>dit';

        // If the text is an account ID or similar, break it intelligently
        // First add breaks before capital letters
        let formattedText = text.replace(/([a-z])([A-Z])/g, '$1<br>$2');

        // Then add breaks for underscore characters
        formattedText = formattedText.replace(/_/g, '<br>');

        return formattedText;
    }
}