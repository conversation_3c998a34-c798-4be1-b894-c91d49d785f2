import { Modal } from './modal'
import {FIVE_MINUTES} from "../shared/constants.js";
import {logger} from "../shared/logger-client.js";

export function initRoomFinder() {
    const button = document.getElementById('tackleBox')
    const executeBtn = document.getElementById('executeScrollFind')
    const getObservationsBtn = document.getElementById('getObservationsBtn')
    const roomHookBtn = document.getElementById('roomHookButton')
    const checkFlaggedRoomModalBtn = document.getElementById('checkFlaggedRoomModalBtn')
    const acceptInvitesBtn = document.getElementById('acceptInvitesButton')
    const input = document.getElementById('roomNameInput')
    const status = document.getElementById('roomFindStatus')
    const requestCountInput = document.getElementById('requestCountInput')
    const requestsModalStatus = document.getElementById('requestsModalStatus')
    const confirmAcceptRequestsBtn = document.getElementById('confirmAcceptRequests')
    const cancelAcceptRequestsBtn = document.getElementById('cancelAcceptRequests')



    const roomFindModal = new Modal('roomFindModal', {
        closeOnClickOutside: true,
        animationDuration: 200,
        title: 'Tackle box',
        onShow: async () => {
            // Get current chat tab
            const tabs = await chrome.tabs.query({
                url: "https://chat.reddit.com/*"
            });

            const chatTab = tabs[0];
            if (chatTab?.url) {
                const match = chatTab.url.match(/chat\.reddit\.com\/room\/(.+)$/);
                if (match) {
                    // Decode URL-encoded characters (like %3A to :)
                    const roomId = decodeURIComponent(match[1]);
                    input.value = roomId;
                } else {
                    input.value = '';
                }
            } else {
                input.value = '';
            }

            status.textContent = ''
            setTimeout(() => input.focus(), 100)
        },
        onHide: () => {
            console.log('Room find modal hidden')
        },
    })

    button?.addEventListener('click', () => roomFindModal.show())


    input?.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            e.preventDefault()
            executeBtn.click()
        }
    })

    executeBtn?.addEventListener('click', async (e) => {
        e.preventDefault()
        const roomName = input.value.trim()
        if (!roomName) {
            status.textContent = 'Please enter a room name'
            status.className = 'status-message error'
            return
        }

        status.textContent = 'Searching...'
        status.className = 'status-message pending'

        try {
            const response = await chrome.runtime.sendMessage({
                action: 'FIND_ROOM',
                roomName: roomName,  // Make sure we're using roomName consistently
            })

            if (response.success) {
                status.textContent = 'Room found!'
                status.className = 'status-message success'
                setTimeout(() => {
                    roomFindModal.hide()
                }, 1500)
            } else {
                status.textContent = response.message || 'Room not found'
                status.className = 'status-message error'
            }
        } catch (error) {
            console.error('Find room error:', error)
            status.textContent = `Error: ${error.message}`
            status.className = 'status-message error'
        }
    })

    getObservationsBtn?.addEventListener('click', async (e) => {
        e.preventDefault()
        const roomId = input.value.trim()
        if (!roomId) {
            status.textContent = 'Please enter a room ID'
            status.className = 'status-message error'
            return
        }

        status.textContent = 'Fetching and sending observations...'
        status.className = 'status-message pending'

        try {
            const response = await chrome.runtime.sendMessage({
                action: 'GET_AND_SEND_ROOM_OBSERVATIONS',
                roomId: 0,
                numRecent: 1
            })

            if (response.success) {
                status.textContent = response.message
                status.className = 'status-message success'

                setTimeout(() => {
                    roomFindModal.hide()
                }, 1500)
            } else {
                throw new Error(response.error)
            }
        } catch (error) {
            console.error('Get observations error:', error)
            status.textContent = `Error: ${error.message}`
            status.className = 'status-message error'
        }
    })
    
    roomHookBtn?.addEventListener('click', async (e) => {
        e.preventDefault()

        // Get current chat tab
        const tabs = await chrome.tabs.query({
            url: "https://chat.reddit.com/*"
        });

        const chatTab = tabs[0];
        if (!chatTab?.url) {
            console.error('No chat tab found');
            return;
        }

        const match = chatTab.url.match(/chat\.reddit\.com\/room\/(.+)$/);
        if (!match) {
            console.error('Not in a chat room');
            return;
        }

        const roomId = decodeURIComponent(match[1]);

        try {
            const response = await chrome.runtime.sendMessage({
                action: 'GET_AND_SEND_ROOM_OBSERVATIONS',
                roomId: roomId
            })

            if (response.success) {
                // Show a temporary success message
                const tempStatus = document.createElement('div');
                tempStatus.textContent = 'Observation sent!';
                tempStatus.style.position = 'fixed';
                tempStatus.style.top = '20px';
                tempStatus.style.right = '20px';
                tempStatus.style.padding = '10px';
                tempStatus.style.backgroundColor = '#4CAF50';
                tempStatus.style.color = 'white';
                tempStatus.style.borderRadius = '4px';
                tempStatus.style.zIndex = '9999';

                document.body.appendChild(tempStatus);

                setTimeout(() => {
                    tempStatus.remove();
                }, 2000);
            } else {
                throw new Error(response.error);
            }
        } catch (error) {
            console.error('Quick observation error:', error);
            // Show error message
            const tempStatus = document.createElement('div');
            tempStatus.textContent = `Error: ${error.message}`;
            tempStatus.style.position = 'fixed';
            tempStatus.style.top = '20px';
            tempStatus.style.right = '20px';
            tempStatus.style.padding = '10px';
            tempStatus.style.backgroundColor = '#f44336';
            tempStatus.style.color = 'white';
            tempStatus.style.borderRadius = '4px';
            tempStatus.style.zIndex = '9999';

            document.body.appendChild(tempStatus);

            setTimeout(() => {
                tempStatus.remove();
            }, 3000);
        }
    })
    
    checkFlaggedRoomModalBtn?.addEventListener('click', async (e) => {
        e.preventDefault()
        const tabs = await chrome.tabs.query({ url: "https://chat.reddit.com/*" })
        const chatTab = tabs[0]
        if (!chatTab?.url) return
        const match = chatTab.url.match(/chat\.reddit\.com\/room\/(.+)$/)
        if (!match) return
        try {
            const response = await chrome.scripting.executeScript({
                target: { tabId: chatTab.id },
                func: () => new window.rc_rooms.query.get_flagged_spaces()
            })
            if (response && response[0] && response[0].result) logger("Seren.GetFlaggedRooms", 'Received flagged rooms', 'success',{ "flagged": response[0].result })
        } catch (error) {}
    })

    // Get or initialize auto-accept setting
    const autoAcceptToggle = document.getElementById('autoAcceptToggle');
    const autoAcceptStatus = document.getElementById('autoAcceptStatus');
    const inviteCounter = document.getElementById('inviteCounter');
    const roomHookToggle = document.getElementById('roomHookToggle');
    const toggles = [autoAcceptToggle, roomHookToggle];
    
    // Update the invite counter with gradient based on usage
    function updateInviteCounter(current, max) {
        if (!inviteCounter) return;

        // Ensure we have valid numbers
        const currentVal = parseInt(current) || 0;
        const maxVal = parseInt(max) || 1; // Avoid division by zero

        // Update the counter text
        inviteCounter.textContent = `${currentVal}/${maxVal}`;

        // Calculate percentage for gradient (capped at 100%)
        const percentage = Math.min(currentVal / maxVal, 1);

        // Calculate color based on percentage (green to red gradient)
        let r, g;
        if (percentage < 0.5) {
            // From green to yellow
            r = Math.floor(255 * (percentage * 2));
            g = 215;
        } else {
            // From yellow to red
            r = 255;
            g = Math.floor(215 * (2 - percentage * 2));
        }

        // Update the gradient

        // Reset the input and status when the modal is shown
        if (requestCountInput) {
            requestCountInput.value = '1' // Default to accepting 1 request
        }
        if (requestsModalStatus) {
            requestsModalStatus.textContent = ''
            requestsModalStatus.className = 'status-message'
        }
        // Load the current settings
         loadAutoAcceptSetting();
         loadMaxInvitesSetting();
    }

    // Accept Requests modal
    const acceptRequestsModal = new Modal('acceptRequestsModal', {
        closeOnClickOutside: true,
        animationDuration: 200,
        title: 'Accept Chat Requests',
        onShow: async () => {
            // Reset the input and status when the modal is shown
            if (requestCountInput) {
                requestCountInput.value = '1' // Default to accepting 1 request
            }
            if (requestsModalStatus) {
                requestsModalStatus.textContent = ''
                requestsModalStatus.className = 'status-message'
            }
            // Load the current settings
            await loadAutoAcceptSetting();
            await loadMaxInvitesSetting();
        },
        onHide: async () => {
            await loadMaxInvitesSetting();
            const enabled = await loadAutoAcceptSetting();
            await updateAutoAcceptStatus(enabled);
            const result = await chrome.storage.local.get(['seren.maxInvites', 'acceptedInvitations']);
            const max = result['seren.maxInvites'] || 0;
            const now = new Date();
            const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            const todayInvites = (result['acceptedInvitations']||[]).filter(invite => {
                const inviteTime = new Date(invite.timestamp);
                return inviteTime > oneDayAgo;
            }).length;
            document.querySelectorAll('.invite-counter').forEach(el => {
                el.textContent = max ? `${todayInvites}/${max}` : `${todayInvites}`;
            });
            console.log('Accept requests modal hidden');
        },
    });

    toggles.forEach(toggle => toggle?.addEventListener('change', async (e) => {
        const enabled = e.target.checked;
        toggles.forEach(t => { if (t && t !== e.target) t.checked = enabled; });
        await chrome.storage.local.set({ autoacceptInvites: enabled });
        if (toggle === autoAcceptToggle) {
            const status = document.getElementById('requestsModalStatus');
            if (status) {
                status.textContent = enabled 
                    ? 'Auto-accept is now enabled' 
                    : 'Auto-accept is now disabled';
                status.className = 'status-message success';
                setTimeout(() => {
                    if (status.textContent.includes('Auto-accept')) {
                        status.textContent = '';
                        status.className = 'status-message';
                    }
                }, 2000);
            }
        }
    }));
    
    async function syncToggles() {
        const result = await chrome.storage.local.get('autoacceptInvites');
        toggles.forEach(t => { if (t) t.checked = !!result.autoacceptInvites; });
    }
    syncToggles();
    async function updateAutoAcceptStatus(enabled) {
        if (autoAcceptToggle) {
            autoAcceptToggle.checked = enabled;
        }
        if (autoAcceptStatus) {
            try {
                const result = await chrome.storage.local.get('seren.maxInvites');
                const maxInvites = result['seren.maxInvites'] || 0;
                const statusText = `${enabled ? 'auto' : 'manual'} Max: ${maxInvites}`;
                autoAcceptStatus.textContent = statusText;
                autoAcceptStatus.className = `auto-accept-status ${enabled ? 'auto' : 'manual'}`;
            } catch (error) {
                console.error('Error updating status:', error);
                autoAcceptStatus.textContent = enabled ? 'auto' : 'manual';
                autoAcceptStatus.className = `auto-accept-status ${enabled ? 'auto' : 'manual'}`;
            }
        }
    }

    async function loadAutoAcceptSetting() {
        try {
            const result = await chrome.storage.local.get(['autoacceptInvites', 'seren.maxInvites']);
            const enabled = result.autoacceptInvites !== false; // Default to true if not set
            await updateAutoAcceptStatus(enabled);
            return enabled;
        } catch (error) {
            console.error('Error loading auto-accept setting:', error);
            await updateAutoAcceptStatus(true); // Default to true on error
            return true;
        }
    }

    // Max invites per day functionality
    const maxInvitesInput = document.getElementById('maxInvitesInput');
    const saveMaxInvitesBtn = document.getElementById('saveMaxInvites');
    const addTenInvitesBtn = document.getElementById('addTenInvites');
    
    // Load saved max invites setting
    async function loadMaxInvitesSetting() {
        try {
            const result = await chrome.storage.local.get(['seren.maxInvites', 'acceptedInvitations']);
            
            // Set max invites input
            if (result['seren.maxInvites'] !== undefined) {
                maxInvitesInput.value = result['seren.maxInvites'];
                
                // Count today's accepted invites
                const now = new Date();
                const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                const todayInvites = result['acceptedInvitations'].filter(invite => {
                  const inviteTime = new Date(invite.timestamp);
                  return inviteTime > oneDayAgo;
                }).length;
                // Update counter
                updateInviteCounter(todayInvites, result['seren.maxInvites']);
                
                return result['seren.maxInvites'];
            }
            return null;
        } catch (error) {
            console.error('Error loading max invites setting:', error);
            return null;
        }
    }
    
    // Save max invites setting
    async function saveMaxInvitesSetting() {
        const value = maxInvitesInput.value.trim();
        const numValue = value ? parseInt(value, 10) : null;
        
        if (value && (isNaN(numValue) || numValue < 0)) {
            showStatusMessage('Please enter a valid number (0 or higher)', 'error');
            return;
        }
        
        try {
            await chrome.storage.local.set({ 'seren.maxInvites': numValue });
            
            // Update the counter with new max value if the counter exists
            if (inviteCounter) {
                const currentCount = parseInt(inviteCounter.textContent.split('/')[0]) || 0;
                updateInviteCounter(currentCount, numValue || 0);
            }
            
            // Update the auto-accept status to show the new max invites
            if (autoAcceptStatus) {
                const enabled = autoAcceptToggle?.checked || false;
                const statusText = `Accept ${enabled ? 'auto' : 'manual'} ( max ${numValue || 0})`;
                autoAcceptStatus.textContent = statusText;
            }
            
            showStatusMessage(numValue !== null 
                ? `Max invites set to ${numValue} per day` 
                : 'Max invites limit removed', 'success');
            return true;
        } catch (error) {
            console.error('Error saving max invites setting:', error);
            showStatusMessage('Failed to save max invites setting', 'error');
            return false;
        }
    }
    
    // Helper function to show status messages
    function showStatusMessage(message, type) {
        const status = document.getElementById('requestsModalStatus');
        if (status) {
            status.textContent = message;
            status.className = `status-message ${type || ''}`;
            
            if (type === 'success') {
                setTimeout(() => {
                    if (status.textContent === message) {
                        status.textContent = '';
                        status.className = 'status-message';
                    }
                }, 3000);
            }
        }
    }
    
    // Event listeners for max invites
    saveMaxInvitesBtn?.addEventListener('click', async (e) => {
        e.preventDefault();
        await saveMaxInvitesSetting();
    });
    
    maxInvitesInput?.addEventListener('keypress', async (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            await saveMaxInvitesSetting();
        }
    });
    
    addTenInvitesBtn?.addEventListener('click', async (e) => {
        e.preventDefault();
        const currentValue = parseInt(maxInvitesInput.value || '0', 10);
        const newValue = currentValue + 10;
        maxInvitesInput.value = newValue;
        await saveMaxInvitesSetting();
    });

    const resetAcceptedInvitesBtn = document.getElementById('resetAcceptedInvites');
    resetAcceptedInvitesBtn?.addEventListener('click', async (e) => {
        e.preventDefault();
        await chrome.storage.local.set({ acceptedInvitations: [] });
        showStatusMessage('Accepted invitations reset', 'success');
        await loadMaxInvitesSetting();
        if (inviteCounter) {
            const max = maxInvitesInput?.value || 0;
            inviteCounter.textContent = max ? `0/${max}` : '0';
        }
    });
    
    // Initial load of settings
    loadAutoAcceptSetting();
    loadMaxInvitesSetting();

    // Update invite-counter every 5 seconds
    setInterval(loadMaxInvitesSetting, 5000);
    
    // Add event listener for the Accept Requests button
    acceptInvitesBtn?.addEventListener('click', (e) => {
        e.preventDefault();
        // Load the current setting before showing the modal
        loadAutoAcceptSetting().then(() => {
            acceptRequestsModal.show();
        });
    });

    // Add event listener for the Cancel button in the Accept Requests modal
    cancelAcceptRequestsBtn?.addEventListener('click', () => {
        acceptRequestsModal.hide()
    })

    // Add event listener for the Confirm button in the Accept Requests modal
    confirmAcceptRequestsBtn?.addEventListener('click', async () => {
        // Get the number of requests to accept (default to 1 if not specified)
        const count = parseInt(requestCountInput?.value || '1', 10)

        if (isNaN(count) || count < 1) {
            if (requestsModalStatus) {
                requestsModalStatus.textContent = 'Please enter a valid number'
                requestsModalStatus.className = 'status-message error'
            }
            return
        }

        if (requestsModalStatus) {
            requestsModalStatus.textContent = 'Processing...'
            requestsModalStatus.className = 'status-message pending'
        }

        try {
            const response = await chrome.runtime.sendMessage({
                action: 'ACCEPT_INVITES',
                count: count
            })

            if (response.success) {
                if (requestsModalStatus) {
                    requestsModalStatus.textContent = response.invitesAccepted > 0
                        ? `Accepted ${response.invitesAccepted} request(s)! For roomid ${response.roomId}`
                        : 'No pending requests found'
                    requestsModalStatus.className = 'status-message success'
                }

                // Close the modal after a short delay on success
                setTimeout(() => {
                    acceptRequestsModal.hide()

                    // Show a temporary status message
                    const tempStatus = document.createElement('div')
                    tempStatus.textContent = response.invitesAccepted > 0
                        ? `Accepted ${response.invitesAccepted} request(s)!`
                        : 'No pending requests found'
                    tempStatus.style.position = 'fixed'
                    tempStatus.style.top = '20px'
                    tempStatus.style.right = '20px'
                    tempStatus.style.padding = '10px'
                    tempStatus.style.backgroundColor = '#4CAF50'
                    tempStatus.style.color = 'white'
                    tempStatus.style.borderRadius = '4px'
                    tempStatus.style.zIndex = '9999'

                    document.body.appendChild(tempStatus)

                    setTimeout(() => {
                        tempStatus.remove()
                    }, 2000)
                }, 1500)
            } else {
                if (requestsModalStatus) {
                    requestsModalStatus.textContent = `Error: ${response.error || 'Failed to accept requests'}`
                    requestsModalStatus.className = 'status-message error'
                }
            }
        } catch (error) {
            console.error('Accept requests error:', error)

            if (requestsModalStatus) {
                requestsModalStatus.textContent = `Error: ${error.message}`
                requestsModalStatus.className = 'status-message error'
            }
        }
    })
}
