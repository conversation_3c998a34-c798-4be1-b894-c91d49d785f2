async get_peek_rooms_info(roomIds) {
    if (!Array.isArray(roomIds) || roomIds.length === 0) return [];
    const db = await this.open_database();
    const tx = db.transaction(['sync'], 'readonly');
    const results = [];
    await this.execute_cursor(tx.objectStore('sync'), cursor => {
        const roomsData = cursor.value.roomsData.peek;
        if (!roomsData) return;
        roomIds.forEach(roomId => {
            const roomInfo = roomsData[roomId];
            if (!roomInfo) return;
            const timeline = roomInfo.timeline?.events || [];
            const stateEvents = roomInfo.state?.events || [];
            for (let i = timeline.length - 1; i >= 0; i--) {
                const event = timeline[i];
                if (event.type === 'm.room.message') {
                    results.push({
                        roomId,
                        senderName: this.get_sender_display_name(stateEvents, event.sender),
                        sender: event.sender,
                        time: new Date(event.origin_server_ts).toISOString(),
                        messageId: event.event_id
                    });
                    break;
                }
            }
        });
    });
    return results;
}